# Student Card Auto Verifier Chrome Extension

This is a Chrome extension that automatically verifies student eligibility for Google One Student and fills SheerID forms.

## Features

- Automatically extracts student information from web pages
- Fills SheerID verification forms
- Integrates with the main Student Card Generator web app
- Supports multiple university formats

## Installation

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select this folder
4. The extension will be installed and ready to use

## Usage

1. Navigate to a page with student information
2. Click the extension icon in the toolbar
3. The extension will automatically extract and verify student data
4. It can also integrate with the main web app at `http://localhost:3000` or `https://card.loading99.site`

## Files

- `manifest.json` - Extension configuration
- `background.js` - Background service worker
- `content.js` - Content script for page interaction
- `popup.html` - Extension popup interface
- `popup.js` - Popup functionality
- `icons/` - Extension icons

## Version

Current version: 2.1.0

## Note

This extension is a separate component from the main Student Card Generator web application. It's designed to work alongside the web app but can function independently.
