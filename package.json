{"name": "generator-card-student", "version": "2.7.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-toast": "^1.2.14", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.9", "html2canvas-pro": "^1.5.11", "jspdf": "^3.0.1", "joi": "^17.13.3", "lucide-react": "^0.525.0", "next": "15.4.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-loading-skeleton": "^3.5.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "validator": "^13.12.0", "zod": "^4.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/validator": "^13.12.2", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}